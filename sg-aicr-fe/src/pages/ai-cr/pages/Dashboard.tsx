// 使用 Stone 统一依赖管理 - 基于参考项目的精确复制
import React from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { useHistory } from '@rome/stone/react-router-dom'
// React Icons 导入
import {
  HiOutlineMagnifyingGlass,
  HiOutlineClock,
  HiOutlineCheckCircle,
  HiOutlineBugAnt,
  HiOutlineArrowTrendingUp,
  HiOutlineTrophy,
  HiOutlinePlus,
  HiOutlineClipboardDocumentList,
  HiOutlineCog6Tooth
} from 'react-icons/hi2'
import './Dashboard.scss'

const Dashboard: React.FC = observer(() => {
  const history = useHistory()
  
  // 模拟数据
  const stats = {
    totalReviews: 156,
    pendingReviews: 23,
    completedReviews: 133,
    issuesFound: 45,
  }

  const recentReviews = [
    {
      id: 1,
      title: 'feat: 添加用户认证模块',
      author: '张三',
      status: 'pending',
      createdAt: '2小时前',
      issues: 3,
    },
    {
      id: 2,
      title: 'fix: 修复登录页面样式问题',
      author: '李四',
      status: 'completed',
      createdAt: '4小时前',
      issues: 1,
    },
    {
      id: 3,
      title: 'refactor: 重构API接口层',
      author: '王五',
      status: 'reviewing',
      createdAt: '6小时前',
      issues: 5,
    },
    {
      id: 4,
      title: 'docs: 更新README文档',
      author: '赵六',
      status: 'completed',
      createdAt: '1天前',
      issues: 0,
    },
  ]

  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审查' },
      reviewing: { color: 'blue', text: '审查中' },
      completed: { color: 'green', text: '已完成' },
    }
    const config = statusMap[status as keyof typeof statusMap]
    return (
      <span 
        style={{
          background: config.color === 'orange' ? '#f59e0b' : config.color === 'blue' ? '#3b82f6' : '#10b981',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '12px',
          fontSize: '11px',
          fontWeight: 600
        }}
      >
        {config.text}
      </span>
    )
  }

  const getStatusIcon = (status: string) => {
    const iconMap = {
      pending: React.createElement(HiOutlineClock as any),
      reviewing: React.createElement(HiOutlineMagnifyingGlass as any),
      completed: React.createElement(HiOutlineCheckCircle as any),
    }
    return iconMap[status as keyof typeof iconMap]
  }

  return (
    <div className="page-container" style={{ width: '100%' }}>
      {/* 页面内容 - 移除大标题，直接显示内容 */}
      <div className="page-content" style={{ width: '2400px', margin: '0 auto' }}>
        {/* 统计卡片 */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-card-header">
              <div className="stat-card-icon primary">
                {React.createElement(HiOutlineMagnifyingGlass as any)}
              </div>
            </div>
            <div className="stat-value">{stats.totalReviews}</div>
            <div className="stat-label">总审查数</div>
            <div className="stat-change positive">
              {React.createElement(HiOutlineArrowTrendingUp as any)} +12% 本周
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-card-header">
              <div className="stat-card-icon warning">
                {React.createElement(HiOutlineClock as any)}
              </div>
            </div>
            <div className="stat-value">{stats.pendingReviews}</div>
            <div className="stat-label">待审查</div>
            <div className="stat-change neutral">
              {React.createElement(HiOutlineClock as any)} 待处理
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-card-header">
              <div className="stat-card-icon success">
                {React.createElement(HiOutlineCheckCircle as any)}
              </div>
            </div>
            <div className="stat-value">{stats.completedReviews}</div>
            <div className="stat-label">已完成</div>
            <div className="stat-change positive">
              {React.createElement(HiOutlineArrowTrendingUp as any)} +8% 本周
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-card-header">
              <div className="stat-card-icon error">
                {React.createElement(HiOutlineBugAnt as any)}
              </div>
            </div>
            <div className="stat-value">{stats.issuesFound}</div>
            <div className="stat-label">发现问题</div>
            <div className="stat-change negative">
              {React.createElement(HiOutlineBugAnt as any)} 需关注
            </div>
          </div>
        </div>

        <div className="content-grid">
          {/* 最近审查 */}
          <div className="chart-card">
            <div className="chart-title">
              {React.createElement(HiOutlineMagnifyingGlass as any)} 最近审查
              <a
                onClick={() => history.push('/code-review')}
                style={{
                  color: '#667eea',
                  fontSize: '14px',
                  cursor: 'pointer',
                  marginLeft: 'auto',
                  fontWeight: 500
                }}
              >
                查看全部
              </a>
            </div>
            <div className="activity-list" style={{ padding: 0, background: 'transparent', border: 'none', boxShadow: 'none' }}>
              {recentReviews.map((item) => (
                <div key={item.id} className="activity-item">
                  <div className={`activity-avatar ${item.status === 'completed' ? 'success' : item.status === 'pending' ? 'warning' : 'primary'}`} style={{
                    background: item.status === 'completed'
                      ? 'linear-gradient(135deg, #10b981 0%, #047857 100%)'
                      : item.status === 'pending'
                      ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
                      : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                  }}>
                    {getStatusIcon(item.status)}
                  </div>
                  <div className="activity-content">
                    <div className="activity-text">
                      <a href={`/code-review/${item.id}`} style={{ fontSize: '14px', fontWeight: 500, color: '#1e293b' }}>
                        {item.title}
                      </a>
                      {item.issues > 0 && (
                        <span style={{ 
                          background: '#ef4444',
                          color: 'white',
                          padding: '2px 6px',
                          borderRadius: '8px',
                          fontSize: '11px',
                          marginLeft: '8px',
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: '2px'
                        }}>
                          {React.createElement(HiOutlineBugAnt as any)} {item.issues}
                        </span>
                      )}
                    </div>
                    <div className="activity-time">
                      作者: {item.author} · {item.createdAt}
                    </div>
                  </div>
                  {getStatusTag(item.status)}
                </div>
              ))}
            </div>
          </div>

          {/* 右侧信息区域 */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
            {/* 审查进度 */}
            <div className="chart-card">
              <div className="chart-title">
                {React.createElement(HiOutlineTrophy as any)} 审查进度
              </div>
              <div style={{ marginBottom: 20 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <span style={{ fontSize: '14px', color: '#475569' }}>本周完成率</span>
                  <span style={{ fontSize: '14px', fontWeight: 600, color: '#1e293b' }}>75%</span>
                </div>
                <div style={{
                  height: '8px',
                  background: '#f1f5f9',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    height: '100%',
                    width: '75%',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '4px',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>
              <div style={{ marginBottom: 20 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <span style={{ fontSize: '14px', color: '#475569' }}>代码质量评分</span>
                  <span style={{ fontSize: '14px', fontWeight: 600, color: '#1e293b' }}>88%</span>
                </div>
                <div style={{
                  height: '8px',
                  background: '#f1f5f9',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    height: '100%',
                    width: '88%',
                    background: 'linear-gradient(135deg, #10b981 0%, #047857 100%)',
                    borderRadius: '4px',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <span style={{ fontSize: '14px', color: '#475569' }}>问题修复率</span>
                  <span style={{ fontSize: '14px', fontWeight: 600, color: '#1e293b' }}>92%</span>
                </div>
                <div style={{
                  height: '8px',
                  background: '#f1f5f9',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    height: '100%',
                    width: '92%',
                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                    borderRadius: '4px',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="chart-card">
              <div className="chart-title">
                {React.createElement(HiOutlinePlus as any)} 快速操作
              </div>
              <div className="quick-actions">
                <div className="quick-action-card" onClick={() => history.push('/')}>
                  <div className="quick-action-icon">
                    {React.createElement(HiOutlinePlus as any)}
                  </div>
                  <div className="quick-action-title">开始审查</div>
                  <div className="quick-action-desc">启动新的代码审查</div>
                </div>
                <div className="quick-action-card" onClick={() => history.push('/code-review')}>
                  <div className="quick-action-icon">
                    {React.createElement(HiOutlineClipboardDocumentList as any)}
                  </div>
                  <div className="quick-action-title">待处理PR</div>
                  <div className="quick-action-desc">查看待审查的PR</div>
                </div>
                <div className="quick-action-card" onClick={() => history.push('/rule-config')}>
                  <div className="quick-action-icon">
                    {React.createElement(HiOutlineCog6Tooth as any)}
                  </div>
                  <div className="quick-action-title">审查规则</div>
                  <div className="quick-action-desc">配置审查规则</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})

export default Dashboard
