// 使用 Stone 统一依赖管理 - 基于参考项目的精确复制
import React, { useState } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { SettingsIcon, UserIcon, ActionIcons } from '../components/Icons'
import './Settings.css'

const Settings: React.FC = observer(() => {
  const [activeTab, setActiveTab] = useState('profile')
  const [settings, setSettings] = useState({
    username: '用户',
    email: '<EMAIL>',
    theme: 'light',
    language: 'zh-CN',
    emailNotifications: true,
    desktopNotifications: false,
    autoSave: true,
    codeReviewMode: 'standard'
  })

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const tabs = [
    { key: 'profile', label: '个人资料', icon: '👤' },
    { key: 'system', label: '系统设置', icon: '⚙️' },
    { key: 'notifications', label: '通知设置', icon: '🔔' },
    { key: 'advanced', label: '高级设置', icon: '🔧' }
  ]

  return (
    <div className="page-container">
      <div className="page-content">
        <div className="settings-layout">
          {/* 左侧导航 */}
          <div className="settings-nav">
            <div className="settings-nav-header">
              <SettingsIcon size={24} />
              <h3>设置</h3>
            </div>
            <div className="settings-nav-list">
              {tabs.map(tab => (
                <button
                  key={tab.key}
                  className={`settings-nav-item ${activeTab === tab.key ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.key)}
                >
                  <span className="nav-icon">{tab.icon}</span>
                  <span className="nav-label">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* 右侧内容 */}
          <div className="settings-content">
            {activeTab === 'profile' && (
              <div className="settings-section">
                <div className="section-header">
                  <h2>个人资料</h2>
                  <p>管理您的个人信息和偏好设置</p>
                </div>

                <div className="settings-form">
                  <div className="form-group">
                    <label>用户名</label>
                    <input
                      type="text"
                      value={settings.username}
                      onChange={(e) => handleSettingChange('username', e.target.value)}
                      className="form-input"
                    />
                  </div>

                  <div className="form-group">
                    <label>邮箱地址</label>
                    <input
                      type="email"
                      value={settings.email}
                      onChange={(e) => handleSettingChange('email', e.target.value)}
                      className="form-input"
                    />
                  </div>

                  <div className="form-group">
                    <label>头像</label>
                    <div className="avatar-upload">
                      <div className="avatar-preview">
                        <UserIcon size={48} />
                      </div>
                      <button className="upload-btn">
                        <ActionIcons.add size={16} />
                        上传头像
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'system' && (
              <div className="settings-section">
                <div className="section-header">
                  <h2>系统设置</h2>
                  <p>配置系统外观和行为</p>
                </div>

                <div className="settings-form">
                  <div className="form-group">
                    <label>主题模式</label>
                    <select
                      value={settings.theme}
                      onChange={(e) => handleSettingChange('theme', e.target.value)}
                      className="form-select"
                    >
                      <option value="light">浅色主题</option>
                      <option value="dark">深色主题</option>
                      <option value="auto">跟随系统</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>界面语言</label>
                    <select
                      value={settings.language}
                      onChange={(e) => handleSettingChange('language', e.target.value)}
                      className="form-select"
                    >
                      <option value="zh-CN">简体中文</option>
                      <option value="en-US">English</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>代码审查模式</label>
                    <select
                      value={settings.codeReviewMode}
                      onChange={(e) => handleSettingChange('codeReviewMode', e.target.value)}
                      className="form-select"
                    >
                      <option value="fast">快速模式</option>
                      <option value="standard">标准模式</option>
                      <option value="strict">严格模式</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="settings-section">
                <div className="section-header">
                  <h2>通知设置</h2>
                  <p>管理通知偏好和提醒方式</p>
                </div>

                <div className="settings-form">
                  <div className="form-group">
                    <div className="switch-item">
                      <div className="switch-info">
                        <label>邮件通知</label>
                        <p>接收代码审查和任务更新的邮件通知</p>
                      </div>
                      <label className="switch">
                        <input
                          type="checkbox"
                          checked={settings.emailNotifications}
                          onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
                        />
                        <span className="slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="form-group">
                    <div className="switch-item">
                      <div className="switch-info">
                        <label>桌面通知</label>
                        <p>在浏览器中显示桌面通知</p>
                      </div>
                      <label className="switch">
                        <input
                          type="checkbox"
                          checked={settings.desktopNotifications}
                          onChange={(e) => handleSettingChange('desktopNotifications', e.target.checked)}
                        />
                        <span className="slider"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'advanced' && (
              <div className="settings-section">
                <div className="section-header">
                  <h2>高级设置</h2>
                  <p>高级功能和实验性特性</p>
                </div>

                <div className="settings-form">
                  <div className="form-group">
                    <div className="switch-item">
                      <div className="switch-info">
                        <label>自动保存</label>
                        <p>自动保存您的工作进度</p>
                      </div>
                      <label className="switch">
                        <input
                          type="checkbox"
                          checked={settings.autoSave}
                          onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
                        />
                        <span className="slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="form-group">
                    <label>数据导出</label>
                    <div className="action-buttons">
                      <button className="action-btn secondary">
                        <ActionIcons.download size={16} />
                        导出设置
                      </button>
                      <button className="action-btn secondary">
                        <ActionIcons.download size={16} />
                        导出数据
                      </button>
                    </div>
                  </div>

                  <div className="form-group">
                    <label>重置选项</label>
                    <div className="action-buttons">
                      <button className="action-btn danger">
                        <ActionIcons.refresh size={16} />
                        重置设置
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 保存按钮 */}
            <div className="settings-actions">
              <button className="action-btn primary">
                保存设置
              </button>
              <button className="action-btn secondary">
                取消
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})

export default Settings
